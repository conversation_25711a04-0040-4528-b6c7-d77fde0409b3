# RS485通信协议规范 - 四象限传感器 (电压值发送模式)
## 面向AI实现的精确协议定义 - V3.1

## 协议参数 (CRITICAL - 必须严格遵守)

```
BAUDRATE: 9600
DATA_BITS: 8
PARITY: NONE
STOP_BITS: 1
DEVICE_ADDRESS: 0x01
FRAME_HEADER: [0xAA, 0x55]
CHECKSUM_ALGORITHM: XOR
```

## 数据类型定义

```c
typedef struct {
    uint8_t header1;        // 固定值 0xAA
    uint8_t header2;        // 固定值 0x55
    uint8_t device_addr;    // 固定值 0x01
    uint8_t data_length;    // 数据字节数
    uint8_t data[];         // 可变长度数据
    uint8_t checksum;       // XOR校验和
} rs485_frame_t;

## 校验和计算算法 (MANDATORY)

```python
def calculate_checksum(data_bytes):
    """
    计算XOR校验和
    data_bytes: 从device_addr开始到最后一个数据字节的所有字节
    返回: 8位校验和
    """
    checksum = 0x00
    for byte in data_bytes:
        checksum ^= byte
    return checksum & 0xFF

# 示例
frame_data = [0x01, 0x02, 0xFF, 0x00]  # [addr, len, data...]
checksum = calculate_checksum(frame_data)  # 结果: 0xFC
```

## 通信模式定义 (重要变更)

### 🔄 主要工作模式: 主动电压数据发送 (V3.1)
```
TRANSMISSION_MODE: ACTIVE_VOLTAGE_PUSH
FREQUENCY: 100ms (10Hz)
BYTE_COUNT: 21
DATA_TYPE: IEEE_754_FLOAT
FORMAT: [0xAA][0x55][0x01][0x10][Q1_V0][Q1_V1][Q1_V2][Q1_V3][Q2_V0][Q2_V1][Q2_V2][Q2_V3][Q3_V0][Q3_V1][Q3_V2][Q3_V3][Q4_V0][Q4_V1][Q4_V2][Q4_V3][CHECKSUM]
```

### 🔧 辅助模式: 命令响应 (保留兼容性)
```
BYTE_COUNT: 5
FORMAT: [0xAA][0x55][0x01][COMMAND][CHECKSUM]
```

### 📡 系统通知 (启动时)
```
BYTE_COUNT: 7
FORMAT: [0xAA][0x55][0x01][0x02][0xFF][0x00][0xFC]
```

## 数据流定义 (CRITICAL_IMPLEMENTATION)

```python
# 主要数据流：连续四象限电压数据 (NEW - V3.1)
QUADRANT_VOLTAGE_STREAM = {
    'format': [0xAA, 0x55, 0x01, 0x10, 'Q1_V0', 'Q1_V1', 'Q1_V2', 'Q1_V3', 'Q2_V0', 'Q2_V1', 'Q2_V2', 'Q2_V3', 'Q3_V0', 'Q3_V1', 'Q3_V2', 'Q3_V3', 'Q4_V0', 'Q4_V1', 'Q4_V2', 'Q4_V3', 'CHK'],
    'frequency_ms': 100,
    'description': '传感器主动发送四象限电压数据',
    'trigger': '连续自动发送，无需查询',
    'data_type': 'IEEE 754浮点数 (32位)',
    'voltage_range': '0-4.096V (单端输入)',
    'precision': '32位浮点精度',
    'priority': 'PRIMARY_DATA_SOURCE'
}

# 系统通知
SYSTEM_NOTIFICATIONS = {
    'SYSTEM_READY': {
        'format': [0xAA, 0x55, 0x01, 0x02, 0xFF, 0x00, 0xFC],
        'trigger': '传感器初始化完成后发送一次',
        'description': '通知主板传感器已就绪'
    }
}

# 兼容性命令 (DEPRECATED - 保留向后兼容)
LEGACY_COMMANDS = {
    'HEARTBEAT': {
        'request': [0xAA, 0x55, 0x01, 0x00, 0x01],
        'response': [0xAA, 0x55, 0x01, 0x01, 0x00, 0x02],
        'note': '已弃用，主要使用主动发送模式'
    },
    'QUERY_DATA': {
        'request': [0xAA, 0x55, 0x01, 0x02, 0x03],
        'response': [0xAA, 0x55, 0x01, 0x08, 'Q1_H', 'Q1_L', 'Q2_H', 'Q2_L', 'Q3_H', 'Q3_L', 'Q4_H', 'Q4_L', 'CHK'],
        'note': '已弃用，数据通过主动发送获取'
    }
}
```

## 主板实现规范 (CRITICAL_FOR_AI)

### 主要接收处理流程 (NEW - V3.0)

```python
def process_continuous_voltage_stream():
    """
    处理传感器主动发送的电压数据流 - V3.1主要实现
    """
    while True:
        # 1. 监听RS485数据
        raw_bytes = rs485_receive_frame()

        # 2. 验证帧格式
        if not validate_frame_header(raw_bytes):
            continue

        # 3. 判断数据类型
        if len(raw_bytes) == 21 and raw_bytes[3] == 0x10:
            # 四象限电压数据帧 (V3.1)
            voltage_data = parse_quadrant_voltage_data(raw_bytes)
            update_sensor_cache(voltage_data)

        elif len(raw_bytes) == 7 and raw_bytes[3] == 0x02:
            # 系统通知帧
            handle_system_notification(raw_bytes)

        else:
            # 其他帧类型 (兼容性)
            handle_legacy_frame(raw_bytes)

def parse_quadrant_voltage_data(frame):
    """
    解析四象限电压数据帧 (V3.1)
    frame: [0xAA, 0x55, 0x01, 0x10, Q1_V0, Q1_V1, Q1_V2, Q1_V3, Q2_V0, Q2_V1, Q2_V2, Q2_V3, Q3_V0, Q3_V1, Q3_V2, Q3_V3, Q4_V0, Q4_V1, Q4_V2, Q4_V3, CHK]
    """
    import struct

    # 验证校验和
    checksum = calculate_checksum(frame[2:20])
    if checksum != frame[20]:
        return None

    # 解析IEEE 754浮点电压值 (小端格式)
    q1_voltage = struct.unpack('<f', bytes(frame[4:8]))[0]
    q2_voltage = struct.unpack('<f', bytes(frame[8:12]))[0]
    q3_voltage = struct.unpack('<f', bytes(frame[12:16]))[0]
    q4_voltage = struct.unpack('<f', bytes(frame[16:20]))[0]

    return {
        'quadrant_1_voltage': q1_voltage,  # 单位: V
        'quadrant_2_voltage': q2_voltage,  # 单位: V
        'quadrant_3_voltage': q3_voltage,  # 单位: V
        'quadrant_4_voltage': q4_voltage,  # 单位: V
        'voltage_range': '±4.096V',
        'timestamp': get_current_time(),
        'valid': True
    }
```

### 数据响应生成函数 (MANDATORY)

```python
def send_heartbeat_response():
    """
    生成心跳响应帧
    返回: [0xAA, 0x55, 0x01, 0x01, 0x00, 0x02]
    """
    frame = [0xAA, 0x55, 0x01, 0x01, 0x00]
    checksum = calculate_checksum(frame[2:])  # 0x01^0x01^0x00 = 0x00
    frame.append(checksum)
    return frame

def send_quadrant_data(q1, q2, q3, q4):
    """
    生成四象限数据响应帧
    q1,q2,q3,q4: 16位ADC值 (0x0000-0xFFFF)
    返回: 13字节数组
    """
    frame = [0xAA, 0x55, 0x01, 0x08]  # 帧头+地址+长度

    # 添加四象限数据 (大端格式)
    frame.extend([
        (q1 >> 8) & 0xFF, q1 & 0xFF,        # Q1高字节,低字节
        (q2 >> 8) & 0xFF, q2 & 0xFF,        # Q2高字节,低字节
        (q3 >> 8) & 0xFF, q3 & 0xFF,        # Q3高字节,低字节
        (q4 >> 8) & 0xFF, q4 & 0xFF         # Q4高字节,低字节
    ])

    # 计算并添加校验和
    checksum = calculate_checksum(frame[2:])
    frame.append(checksum)
    return frame

def send_system_ready():
    """
    生成系统就绪通知帧
    返回: [0xAA, 0x55, 0x01, 0x02, 0xFF, 0x00, 0xFC]
    """
    return [0xAA, 0x55, 0x01, 0x02, 0xFF, 0x00, 0xFC]
```

## 测试用例 (VALIDATION REQUIRED)

```python
# 测试用例1: 心跳命令
TEST_HEARTBEAT = {
    'input': [0xAA, 0x55, 0x01, 0x00, 0x01],
    'expected_output': [0xAA, 0x55, 0x01, 0x01, 0x00, 0x02],
    'description': '心跳测试'
}

# 测试用例2: 数据查询
TEST_QUERY_DATA = {
    'input': [0xAA, 0x55, 0x01, 0x02, 0x03],
    'expected_output_format': [0xAA, 0x55, 0x01, 0x08, 'Q1_H', 'Q1_L', 'Q2_H', 'Q2_L', 'Q3_H', 'Q3_L', 'Q4_H', 'Q4_L', 'CHK'],
    'description': '四象限数据查询'
}

# 测试用例3: 系统就绪通知
TEST_SYSTEM_READY = {
    'trigger': 'power_on_complete',
    'output': [0xAA, 0x55, 0x01, 0x02, 0xFF, 0x00, 0xFC],
    'description': '系统初始化完成通知'
}
```

## 错误处理机制 (ERROR_HANDLING)

```python
ERROR_CODES = {
    0x01: "SENSOR_READ_ERROR",    # CM1103读取失败
    0xFF: "UNKNOWN_COMMAND"       # 不支持的命令
}

def send_error_response(error_code):
    """
    生成错误响应帧
    error_code: 错误码 (0x01 或 0xFF)
    """
    frame = [0xAA, 0x55, 0x01, 0x01, error_code]
    checksum = calculate_checksum(frame[2:])
    frame.append(checksum)
    return frame

# 错误响应示例
ERROR_SENSOR_READ = [0xAA, 0x55, 0x01, 0x01, 0x01, 0x01]  # 校验和: 0x01^0x01^0x01=0x01
ERROR_UNKNOWN_CMD = [0xAA, 0x55, 0x01, 0x01, 0xFF, 0xFF]  # 校验和: 0x01^0x01^0xFF=0xFF
```

## 时序要求 (TIMING_CONSTRAINTS)

```python
TIMING_PARAMETERS = {
    'RESPONSE_DELAY_MS': 20,      # 收到命令后的响应延时
    'FRAME_INTERVAL_MS': 10,      # 帧间最小间隔
    'BYTE_TIMEOUT_MS': 10,        # 字节接收超时
    'DATA_ACQUISITION_MS': 60,    # 四象限数据采集总时间
    'SYSTEM_READY_DELAY_MS': 100  # 系统就绪通知延时
}

# 通信状态机超时处理
def handle_timeout():
    """
    字节间超时处理：重置接收状态机
    """
    reset_receive_state_machine()
```

## 完整实现示例 (REFERENCE_IMPLEMENTATION)

```python
class RS485QuadrantSensorProtocol:
    def __init__(self):
        self.DEVICE_ADDR = 0x01
        self.FRAME_HEADER = [0xAA, 0x55]

    def calculate_checksum(self, data):
        return functools.reduce(lambda x, y: x ^ y, data, 0)

    def parse_command(self, raw_frame):
        """解析接收到的命令帧"""
        if len(raw_frame) != 5:
            return None
        if raw_frame[:3] != [0xAA, 0x55, 0x01]:
            return None
        if self.calculate_checksum(raw_frame[2:4]) != raw_frame[4]:
            return None
        return raw_frame[3]  # 返回命令码

    def handle_heartbeat(self):
        """处理心跳命令"""
        return [0xAA, 0x55, 0x01, 0x01, 0x00, 0x02]

    def handle_query_data(self, q1, q2, q3, q4):
        """处理数据查询命令"""
        frame = [0xAA, 0x55, 0x01, 0x08]
        frame.extend([(q1>>8)&0xFF, q1&0xFF, (q2>>8)&0xFF, q2&0xFF,
                     (q3>>8)&0xFF, q3&0xFF, (q4>>8)&0xFF, q4&0xFF])
        frame.append(self.calculate_checksum(frame[2:]))
        return frame

    def send_system_ready(self):
        """发送系统就绪通知"""
        return [0xAA, 0x55, 0x01, 0x02, 0xFF, 0x00, 0xFC]
```

## 实现检查清单 (IMPLEMENTATION_CHECKLIST)

- [ ] 串口配置: 9600 bps, 8N1
- [ ] 帧头验证: 0xAA 0x55
- [ ] 设备地址验证: 0x01
- [ ] XOR校验和计算和验证
- [ ] 心跳命令响应 (0x00 → 固定响应)
- [ ] 数据查询响应 (0x02 → 8字节ADC数据)
- [ ] 系统就绪通知 (启动后自动发送)
- [ ] 错误处理 (未知命令、传感器错误)
- [ ] 超时处理 (10ms字节超时)
- [ ] 大端格式数据传输

## 关键变更和约束 (CRITICAL_CONSTRAINTS_V3.0)

### 🔄 重要变更 (V3.0 → V3.1)
1. **数据格式**: 从"16位ADC值"改为"32位IEEE 754浮点电压值"
2. **帧长度**: 从13字节增加到21字节
3. **数据精度**: 从整数ADC值提升到浮点电压值
4. **主板处理**: 无需ADC转换，直接使用电压值
5. **数据范围**: ±4.096V，32位浮点精度

### 📋 实现约束 (V3.1)
1. **数据格式**: IEEE 754浮点数，32位，小端格式
2. **校验算法**: 必须使用XOR校验，范围从设备地址到最后一个电压字节
3. **接收处理**: 主板必须能处理100ms频率的21字节数据流
4. **帧完整性**: 任何校验失败的帧必须丢弃
5. **缓存更新**: 收到有效电压数据后立即更新本地缓存
6. **浮点解析**: 使用标准IEEE 754解析库

### 🎯 主板实现要点 (V3.1)
- **监听模式**: 持续监听RS485，无需发送查询命令
- **数据解析**: 重点处理21字节的四象限电压数据帧
- **浮点解析**: 使用IEEE 754标准解析32位浮点电压值
- **缓存管理**: 实时更新四象限电压缓存
- **直接使用**: 电压值可直接用于计算，无需转换
- **兼容性**: 可选择性保留心跳命令支持

### 📊 数据示例
```
接收帧: AA 55 01 10 [Q1_V] [Q2_V] [Q3_V] [Q4_V] CHK
解析结果: Q1=-0.87V, Q2=+1.23V, Q3=-0.45V, Q4=+0.98V
```

---
**协议版本**: V3.1.0 | **模式**: 主动电压发送 | **频率**: 100ms | **数据**: IEEE 754浮点 | **面向**: AI实现

/*!
    \file    cm1103_driver.c
    \brief   CM1103 Quadrant Sensor Driver Implementation - Simplified Version

    \version 2025-07-31, V2.0.0, Simplified CM1103 driver for GD32F310F8P6
    \note    专注于核心功能：通过软件I2C读取四路模拟输入电压
*/

#include "cm1103_driver.h"
#include "systick.h"
#include <stdio.h>

/* 全局变量 */
volatile bool g_cm1103_data_ready = false;
volatile bool g_cm1103_conversion_done = false;

/* 数据缓存 - 连续转换模式下的数据缓存 */
volatile uint16_t g_cached_channel_data[CM1103_MAX_CHANNELS] = {0};
volatile uint32_t g_last_update_time[CM1103_MAX_CHANNELS] = {0};
volatile uint8_t g_current_channel_index = 0;

/* 简化的配置 - 只保留必要的设置 */
static uint16_t current_config = CM1103_CONFIG_DEFAULT;
static cm1103_channel_t current_channel = CM1103_CHANNEL_A;

/*!
    \brief      initialize CM1103 sensor - simplified version
    \param[in]  none
    \param[out] none
    \retval     true: success, false: failed
*/
/*!
    \brief      scan I2C bus for devices (debugging function)
    \param[in]  none
    \param[out] none
    \retval     number of devices found
*/
uint8_t cm1103_i2c_scan(void)
{
    uint8_t devices_found = 0;
    uint16_t dummy_data;

    /* 扫描常见的I2C地址 */
    uint8_t test_addresses[] = {0x48, 0x49, 0x4A, 0x4B};

    for(int i = 0; i < 4; i++) {
        i2c_result_t result = soft_i2c_read_reg(test_addresses[i], 0x00, &dummy_data);
        if(result == I2C_OK) {
            devices_found++;
            /* 找到设备在地址 test_addresses[i] */
        }
    }

    return devices_found;
}

bool cm1103_init(void)
{
    /* 初始化软件I2C */
    soft_i2c_init();

    /* 延时等待设备稳定 - CM1103上电后需要时间稳定 */
    delay_1ms(100);

    /* 扫描I2C总线查找设备 */
    uint8_t devices = cm1103_i2c_scan();
    if(devices == 0) {
        /* 没有找到任何I2C设备 */
        return false;
    }

    /* 测试I2C通信 - 尝试读取转换寄存器验证通信 */
    uint16_t test_data;
    i2c_result_t i2c_result = soft_i2c_read_reg(CM1103_DEVICE_ADDR, CM1103_REG_CONVERSION, &test_data);
    if(i2c_result != I2C_OK) {
        /* I2C通信失败 - 记录错误类型便于调试 */
        // 错误类型: I2C_ERROR_NACK, I2C_ERROR_TIMEOUT, I2C_ERROR_BUS_BUSY
        return false;
    }

    /* 配置CM1103为连续转换模式，±4.096V量程，100 SPS
     * 注意：DRDY引脚在硬件上通过上拉电阻连接到3.3V，不使用中断功能
     * 采用轮询方式读取数据，通过延时确保转换完成
     */
    if(!cm1103_write_register(CM1103_REG_CONFIG, CM1103_CONFIG_DEFAULT)) {
        return false;
    }

    uint16_t read_config;
    if(!cm1103_read_register(CM1103_REG_CONFIG, &read_config)) {
        return false;
    }

    /* 验证关键配置位是否正确写入 */
    if((read_config & CM1103_CONFIG_PGA_MASK) != CM1103_CONFIG_PGA_4096) {
        /* PGA配置验证失败 */
        return false;
    }

    /* 验证比较器是否正确禁用 (COMP_QUE = 11) */
    if((read_config & CM1103_CONFIG_CQUE_MASK) != 0x0003) {
        /* 比较器禁用配置失败 */
        return false;
    }

    /* 初始化缓存数据和时间戳 */
    for(uint8_t i = 0; i < CM1103_MAX_CHANNELS; i++) {
        g_cached_channel_data[i] = 0;
        g_last_update_time[i] = 0;
    }

    /* 设置初始通道为A，从通道0开始 */
    g_current_channel_index = 0;
    current_channel = CM1103_CHANNEL_A;
    if(!cm1103_config_channel(CM1103_CHANNEL_A)) {
        return false;
    }

    /* 延时让设备完全稳定，确保第一次转换完成 */
    delay_1ms(150);  /* 增加延时确保第一次转换完成 */

    return true;
}

/*!
    \brief      update continuous conversion data (call periodically)
    \param[in]  none
    \param[out] none
    \retval     none
*/
void cm1103_update_continuous_data(void)
{
    static uint32_t last_channel_switch = 0;
    static const uint32_t channel_switch_interval = 20; /* 20ms > 1个转换周期(100SPS=10ms) */
    static uint32_t consecutive_errors = 0;  /* 连续错误计数器 */
    static const uint32_t max_consecutive_errors = 5;  /* 最大连续错误次数 */
    static uint8_t reading_channel = 0;  /* 当前正在读取数据的通道 */
    static bool initialized = false;

    uint32_t current_time = systick_get_tick();

    /* 检查是否需要切换通道 */
    if((current_time - last_channel_switch) >= channel_switch_interval) {

        /* 如果已经初始化，读取当前通道的转换数据 */
        if(initialized) {
            uint16_t data;
            if(cm1103_read_conversion(&data)) {
                /* 存储到正确的通道索引 */
                g_cached_channel_data[reading_channel] = data;
                g_last_update_time[reading_channel] = current_time;

                /* 设置数据就绪标志 */
                g_cm1103_data_ready = true;

                /* 重置错误计数器 */
                consecutive_errors = 0;

                /* 简单的数据验证：如果数据为0，可能有问题 */
                if(data == 0) {
                    /* 数据为0可能表示读取有问题，但不一定是错误 */
                    /* 在调试时可以在这里设置断点 */
                }
            } else {
                /* 读取失败，增加错误计数 */
                consecutive_errors++;

                /* 如果连续错误过多，重置缓存 */
                if(consecutive_errors >= max_consecutive_errors) {
                    cm1103_reset_cache();
                    consecutive_errors = 0;
                    initialized = false;  /* 重新初始化 */
                }
            }
        }

        /* 切换到下一个通道 */
        reading_channel = (reading_channel + 1) % CM1103_MAX_CHANNELS;
        g_current_channel_index = reading_channel;

        cm1103_channel_t channels[] = {CM1103_CHANNEL_A, CM1103_CHANNEL_B,
                                       CM1103_CHANNEL_C, CM1103_CHANNEL_D};

        /* 配置新通道，为下次读取做准备 */
        if(cm1103_config_channel(channels[reading_channel])) {
            if(!initialized) {
                initialized = true;  /* 第一次配置成功后标记为已初始化 */
            }
        } else {
            /* 配置失败，增加错误计数 */
            consecutive_errors++;
        }

        last_channel_switch = current_time;
    }
}

/*!
    \brief      configure CM1103 channel
    \param[in]  channel: channel to configure
    \param[out] none
    \retval     true: success, false: failed
*/
bool cm1103_config_channel(cm1103_channel_t channel)
{
    uint16_t config = current_config;

    /* 清除MUX位 */
    config &= ~CM1103_CONFIG_MUX_MASK;

    /* 设置对应通道 */
    switch(channel) {
        case CM1103_CHANNEL_A:
            config |= CM1103_CONFIG_MUX_AIN0;
            break;
        case CM1103_CHANNEL_B:
            config |= CM1103_CONFIG_MUX_AIN1;
            break;
        case CM1103_CHANNEL_C:
            config |= CM1103_CONFIG_MUX_AIN2;
            break;
        case CM1103_CHANNEL_D:
            config |= CM1103_CONFIG_MUX_AIN3;
            break;
        default:
            return false;
    }

    /* 写入配置寄存器，连续转换模式下会自动开始新通道转换 */
    if(!cm1103_write_register(CM1103_REG_CONFIG, config)) {
        return false;
    }

    current_config = config;
    current_channel = channel;

    return true;
}



/*!
    \brief      read CM1103 conversion result
    \param[in]  none
    \param[out] data: pointer to store conversion result
    \retval     true: success, false: failed
*/
bool cm1103_read_conversion(uint16_t *data)
{
    /* 简化版本：直接读取转换结果 */
    /* 在100ms间隔下，转换应该已经完成 */
    return cm1103_read_register(CM1103_REG_CONVERSION, data);
}

/*!
    \brief      convert ADC value to voltage
    \param[in]  adc_value: 16-bit ADC value
    \param[out] none
    \retval     voltage in volts
*/
float cm1103_adc_to_voltage(uint16_t adc_value)
{
    /* CM1103单端输入模式，4.096V量程，16位ADC
     * ADC值范围: 0 ~ 65535
     * 电压范围: 0V ~ 4.096V (单端输入)
     * 转换公式: voltage = adc_value * 4.096 / 65535
     * 注意：单端输入时，0V对应ADC值0，4.096V对应ADC值65535
     */
    float voltage = ((float)adc_value * 4.096f) / 65535.0f;
    return voltage;
}

/*!
    \brief      reset cached data and timestamps
    \param[in]  none
    \param[out] none
    \retval     none
*/
void cm1103_reset_cache(void)
{
    /* 重置所有缓存数据 */
    for(uint8_t i = 0; i < CM1103_MAX_CHANNELS; i++) {
        g_cached_channel_data[i] = 0;
        g_last_update_time[i] = 0;
    }

    /* 重置状态标志 */
    g_cm1103_data_ready = false;
    g_cm1103_conversion_done = false;
    g_current_channel_index = 0;
}

/*!
    \brief      get cache status for debugging
    \param[in]  none
    \param[out] none
    \retval     none
*/
void cm1103_get_cache_status(void)
{
    /* 这个函数可以用于调试，打印当前缓存状态 */
    /* 在调试时可以在这里添加printf语句 */
    /* 例如：
    printf("Current Channel: %d\n", g_current_channel_index);
    printf("Cache Data: [%d, %d, %d, %d]\n",
           g_cached_channel_data[0], g_cached_channel_data[1],
           g_cached_channel_data[2], g_cached_channel_data[3]);
    */
}

/*!
    \brief      check if cached data is valid (not too old)
    \param[in]  none
    \param[out] none
    \retval     true: data is valid, false: data is stale
*/
bool cm1103_is_cached_data_valid(void)
{
    uint32_t current_time = systick_get_tick();
    const uint32_t max_data_age_ms = 500; /* 数据超过500ms认为过期 */

    /* 检查所有通道的数据是否都在有效期内 */
    for(uint8_t i = 0; i < CM1103_MAX_CHANNELS; i++) {
        if(g_last_update_time[i] == 0) {
            /* 通道从未更新过 */
            return false;
        }

        uint32_t data_age = current_time - g_last_update_time[i];
        if(data_age > max_data_age_ms) {
            /* 数据过期 */
            return false;
        }
    }

    return true;
}

/*!
    \brief      read all quadrant channels - simplified version
    \param[in]  none
    \param[out] quad_data: pointer to store quadrant data
    \retval     true: success, false: failed
*/
bool cm1103_read_all_channels(cm1103_quadrant_data_t *quad_data)
{
    /* 检查缓存数据是否有效 */
    if(!cm1103_is_cached_data_valid()) {
        quad_data->valid = false;
        return false;
    }

    /* 使用缓存的数据，无需等待 */
    quad_data->quadrant_a_raw = g_cached_channel_data[0];
    quad_data->quadrant_b_raw = g_cached_channel_data[1];
    quad_data->quadrant_c_raw = g_cached_channel_data[2];
    quad_data->quadrant_d_raw = g_cached_channel_data[3];

    /* 转换为电压值 */
    quad_data->quadrant_a_voltage = cm1103_adc_to_voltage(g_cached_channel_data[0]);
    quad_data->quadrant_b_voltage = cm1103_adc_to_voltage(g_cached_channel_data[1]);
    quad_data->quadrant_c_voltage = cm1103_adc_to_voltage(g_cached_channel_data[2]);
    quad_data->quadrant_d_voltage = cm1103_adc_to_voltage(g_cached_channel_data[3]);

    quad_data->timestamp = systick_get_tick();
    quad_data->valid = true;

    return true;
}



/* ========== 底层寄存器操作函数 ========== */

/*!
    \brief      write CM1103 register
    \param[in]  reg_addr: register address
    \param[in]  reg_data: data to write
    \param[out] none
    \retval     true: success, false: failed
*/
bool cm1103_write_register(uint8_t reg_addr, uint16_t reg_data)
{
    return (soft_i2c_write_reg(CM1103_DEVICE_ADDR, reg_addr, reg_data) == I2C_OK);
}

/*!
    \brief      read CM1103 register
    \param[in]  reg_addr: register address
    \param[out] reg_data: pointer to store read data
    \retval     true: success, false: failed
*/
bool cm1103_read_register(uint8_t reg_addr, uint16_t *reg_data)
{
    return (soft_i2c_read_reg(CM1103_DEVICE_ADDR, reg_addr, reg_data) == I2C_OK);
}



/* 注意：DRDY相关函数已移除
 * 原因：硬件设计中CM1103的RDY引脚通过上拉电阻直接连接到3.3V
 * 不连接到微控制器GPIO，因此不使用DRDY中断功能
 * 改用轮询方式，通过适当的延时确保数据转换完成
 */

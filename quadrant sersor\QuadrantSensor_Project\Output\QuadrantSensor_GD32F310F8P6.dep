Dependencies for Project 'QuadrantSensor', Target 'GD32F310F8P6': (DO NOT MODIFY !)
F (.\User\main.c)(0x688C8B52)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ./Drivers/CMSIS/Include -I ./Drivers/GD32F3x0_StdPeriph_Driver/inc -I ./User -I ./Drivers/CMSIS/Source/ARM

-I./RTE/_GD32F310F8P6

-ID:/Keil5.27/Pack/GigaDevice/GD32F3x0_DFP/3.3.0/Device/Include

-ID:/Keil5.27/Core/ARM/CMSIS/Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o ./output/main.o -MD)
I (Drivers\CMSIS\Include\gd32f3x0.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCLANG\include\stdint.h)(0x5C40E42A)
I (Drivers\CMSIS\Include\core_cmInstr.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cmFunc.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cm4_simd.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (User\gd32f3x0_libopt.h)(0x68849119)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_adc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_gpio.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_rcu.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_usart.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_misc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_fmc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_exti.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_syscfg.h)(0x67BD7BDB)
I (User\systick.h)(0x68848FC1)
I (D:\Keil5.27\Core\ARM\ARMCLANG\include\stdio.h)(0x5C40E42A)
I (User\main.h)(0x67BD7BE0)
I (User\cm1103_driver.h)(0x688DB38C)
I (User\soft_i2c.h)(0x688C816B)
I (D:\Keil5.27\Core\ARM\ARMCLANG\include\stdbool.h)(0x5C40E42A)
I (User\rs485_comm.h)(0x688CAE3C)
F (.\User\gd32f3x0_it.c)(0x688B424C)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ./Drivers/CMSIS/Include -I ./Drivers/GD32F3x0_StdPeriph_Driver/inc -I ./User -I ./Drivers/CMSIS/Source/ARM

-I./RTE/_GD32F310F8P6

-ID:/Keil5.27/Pack/GigaDevice/GD32F3x0_DFP/3.3.0/Device/Include

-ID:/Keil5.27/Core/ARM/CMSIS/Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o ./output/gd32f3x0_it.o -MD)
I (User\gd32f3x0_it.h)(0x67BD7BE0)
I (Drivers\CMSIS\Include\gd32f3x0.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCLANG\include\stdint.h)(0x5C40E42A)
I (Drivers\CMSIS\Include\core_cmInstr.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cmFunc.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cm4_simd.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (User\gd32f3x0_libopt.h)(0x68849119)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_adc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_gpio.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_rcu.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_usart.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_misc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_fmc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_exti.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_syscfg.h)(0x67BD7BDB)
I (User\main.h)(0x67BD7BE0)
I (User\systick.h)(0x68848FC1)
I (User\rs485_comm.h)(0x688CAE3C)
I (User\cm1103_driver.h)(0x688DB38C)
I (User\soft_i2c.h)(0x688C816B)
I (D:\Keil5.27\Core\ARM\ARMCLANG\include\stdbool.h)(0x5C40E42A)
F (.\User\systick.c)(0x68848FE3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ./Drivers/CMSIS/Include -I ./Drivers/GD32F3x0_StdPeriph_Driver/inc -I ./User -I ./Drivers/CMSIS/Source/ARM

-I./RTE/_GD32F310F8P6

-ID:/Keil5.27/Pack/GigaDevice/GD32F3x0_DFP/3.3.0/Device/Include

-ID:/Keil5.27/Core/ARM/CMSIS/Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o ./output/systick.o -MD)
I (Drivers\CMSIS\Include\gd32f3x0.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCLANG\include\stdint.h)(0x5C40E42A)
I (Drivers\CMSIS\Include\core_cmInstr.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cmFunc.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cm4_simd.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (User\gd32f3x0_libopt.h)(0x68849119)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_adc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_gpio.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_rcu.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_usart.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_misc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_fmc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_exti.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_syscfg.h)(0x67BD7BDB)
I (User\systick.h)(0x68848FC1)
F (.\User\cm1103_driver.c)(0x688DB3C8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ./Drivers/CMSIS/Include -I ./Drivers/GD32F3x0_StdPeriph_Driver/inc -I ./User -I ./Drivers/CMSIS/Source/ARM

-I./RTE/_GD32F310F8P6

-ID:/Keil5.27/Pack/GigaDevice/GD32F3x0_DFP/3.3.0/Device/Include

-ID:/Keil5.27/Core/ARM/CMSIS/Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o ./output/cm1103_driver.o -MD)
I (User\cm1103_driver.h)(0x688DB38C)
I (Drivers\CMSIS\Include\gd32f3x0.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCLANG\include\stdint.h)(0x5C40E42A)
I (Drivers\CMSIS\Include\core_cmInstr.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cmFunc.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cm4_simd.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (User\gd32f3x0_libopt.h)(0x68849119)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_adc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_gpio.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_rcu.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_usart.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_misc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_fmc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_exti.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_syscfg.h)(0x67BD7BDB)
I (User\soft_i2c.h)(0x688C816B)
I (D:\Keil5.27\Core\ARM\ARMCLANG\include\stdbool.h)(0x5C40E42A)
I (User\systick.h)(0x68848FC1)
I (D:\Keil5.27\Core\ARM\ARMCLANG\include\stdio.h)(0x5C40E42A)
F (.\User\soft_i2c.c)(0x688C6D55)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ./Drivers/CMSIS/Include -I ./Drivers/GD32F3x0_StdPeriph_Driver/inc -I ./User -I ./Drivers/CMSIS/Source/ARM

-I./RTE/_GD32F310F8P6

-ID:/Keil5.27/Pack/GigaDevice/GD32F3x0_DFP/3.3.0/Device/Include

-ID:/Keil5.27/Core/ARM/CMSIS/Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o ./output/soft_i2c.o -MD)
I (User\soft_i2c.h)(0x688C816B)
I (Drivers\CMSIS\Include\gd32f3x0.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCLANG\include\stdint.h)(0x5C40E42A)
I (Drivers\CMSIS\Include\core_cmInstr.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cmFunc.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cm4_simd.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (User\gd32f3x0_libopt.h)(0x68849119)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_adc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_gpio.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_rcu.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_usart.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_misc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_fmc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_exti.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_syscfg.h)(0x67BD7BDB)
I (D:\Keil5.27\Core\ARM\ARMCLANG\include\stdbool.h)(0x5C40E42A)
I (User\systick.h)(0x68848FC1)
F (.\User\rs485_comm.c)(0x688D9850)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ./Drivers/CMSIS/Include -I ./Drivers/GD32F3x0_StdPeriph_Driver/inc -I ./User -I ./Drivers/CMSIS/Source/ARM

-I./RTE/_GD32F310F8P6

-ID:/Keil5.27/Pack/GigaDevice/GD32F3x0_DFP/3.3.0/Device/Include

-ID:/Keil5.27/Core/ARM/CMSIS/Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o ./output/rs485_comm.o -MD)
I (User\rs485_comm.h)(0x688CAE3C)
I (Drivers\CMSIS\Include\gd32f3x0.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCLANG\include\stdint.h)(0x5C40E42A)
I (Drivers\CMSIS\Include\core_cmInstr.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cmFunc.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cm4_simd.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (User\gd32f3x0_libopt.h)(0x68849119)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_adc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_gpio.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_rcu.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_usart.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_misc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_fmc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_exti.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_syscfg.h)(0x67BD7BDB)
I (User\cm1103_driver.h)(0x688DB38C)
I (User\soft_i2c.h)(0x688C816B)
I (D:\Keil5.27\Core\ARM\ARMCLANG\include\stdbool.h)(0x5C40E42A)
I (User\systick.h)(0x68848FC1)
I (D:\Keil5.27\Core\ARM\ARMCLANG\include\stdio.h)(0x5C40E42A)
I (D:\Keil5.27\Core\ARM\ARMCLANG\include\string.h)(0x5C40E42A)
F (.\Drivers\CMSIS\Source\system_gd32f3x0.c)(0x67BD7BDA)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ./Drivers/CMSIS/Include -I ./Drivers/GD32F3x0_StdPeriph_Driver/inc -I ./User -I ./Drivers/CMSIS/Source/ARM

-I./RTE/_GD32F310F8P6

-ID:/Keil5.27/Pack/GigaDevice/GD32F3x0_DFP/3.3.0/Device/Include

-ID:/Keil5.27/Core/ARM/CMSIS/Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o ./output/system_gd32f3x0.o -MD)
I (Drivers\CMSIS\Include\gd32f3x0.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCLANG\include\stdint.h)(0x5C40E42A)
I (Drivers\CMSIS\Include\core_cmInstr.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cmFunc.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cm4_simd.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (User\gd32f3x0_libopt.h)(0x68849119)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_adc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_gpio.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_rcu.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_usart.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_misc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_fmc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_exti.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_syscfg.h)(0x67BD7BDB)
F (.\Drivers\GD32F3x0_StdPeriph_Driver\src\gd32f3x0_adc.c)(0x67BD7BDB)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ./Drivers/CMSIS/Include -I ./Drivers/GD32F3x0_StdPeriph_Driver/inc -I ./User -I ./Drivers/CMSIS/Source/ARM

-I./RTE/_GD32F310F8P6

-ID:/Keil5.27/Pack/GigaDevice/GD32F3x0_DFP/3.3.0/Device/Include

-ID:/Keil5.27/Core/ARM/CMSIS/Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o ./output/gd32f3x0_adc.o -MD)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_adc.h)(0x67BD7BDB)
I (Drivers\CMSIS\Include\gd32f3x0.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCLANG\include\stdint.h)(0x5C40E42A)
I (Drivers\CMSIS\Include\core_cmInstr.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cmFunc.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cm4_simd.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (User\gd32f3x0_libopt.h)(0x68849119)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_gpio.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_rcu.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_usart.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_misc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_fmc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_exti.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_syscfg.h)(0x67BD7BDB)
F (.\Drivers\GD32F3x0_StdPeriph_Driver\src\gd32f3x0_gpio.c)(0x67BD7BDB)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ./Drivers/CMSIS/Include -I ./Drivers/GD32F3x0_StdPeriph_Driver/inc -I ./User -I ./Drivers/CMSIS/Source/ARM

-I./RTE/_GD32F310F8P6

-ID:/Keil5.27/Pack/GigaDevice/GD32F3x0_DFP/3.3.0/Device/Include

-ID:/Keil5.27/Core/ARM/CMSIS/Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o ./output/gd32f3x0_gpio.o -MD)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_gpio.h)(0x67BD7BDB)
I (Drivers\CMSIS\Include\gd32f3x0.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCLANG\include\stdint.h)(0x5C40E42A)
I (Drivers\CMSIS\Include\core_cmInstr.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cmFunc.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cm4_simd.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (User\gd32f3x0_libopt.h)(0x68849119)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_adc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_rcu.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_usart.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_misc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_fmc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_exti.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_syscfg.h)(0x67BD7BDB)
F (.\Drivers\GD32F3x0_StdPeriph_Driver\src\gd32f3x0_rcu.c)(0x67BD7BDB)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ./Drivers/CMSIS/Include -I ./Drivers/GD32F3x0_StdPeriph_Driver/inc -I ./User -I ./Drivers/CMSIS/Source/ARM

-I./RTE/_GD32F310F8P6

-ID:/Keil5.27/Pack/GigaDevice/GD32F3x0_DFP/3.3.0/Device/Include

-ID:/Keil5.27/Core/ARM/CMSIS/Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o ./output/gd32f3x0_rcu.o -MD)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_rcu.h)(0x67BD7BDB)
I (Drivers\CMSIS\Include\gd32f3x0.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCLANG\include\stdint.h)(0x5C40E42A)
I (Drivers\CMSIS\Include\core_cmInstr.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cmFunc.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cm4_simd.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (User\gd32f3x0_libopt.h)(0x68849119)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_adc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_gpio.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_usart.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_misc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_fmc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_exti.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_syscfg.h)(0x67BD7BDB)
F (.\Drivers\GD32F3x0_StdPeriph_Driver\src\gd32f3x0_usart.c)(0x67BD7BDB)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ./Drivers/CMSIS/Include -I ./Drivers/GD32F3x0_StdPeriph_Driver/inc -I ./User -I ./Drivers/CMSIS/Source/ARM

-I./RTE/_GD32F310F8P6

-ID:/Keil5.27/Pack/GigaDevice/GD32F3x0_DFP/3.3.0/Device/Include

-ID:/Keil5.27/Core/ARM/CMSIS/Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o ./output/gd32f3x0_usart.o -MD)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_usart.h)(0x67BD7BDB)
I (Drivers\CMSIS\Include\gd32f3x0.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCLANG\include\stdint.h)(0x5C40E42A)
I (Drivers\CMSIS\Include\core_cmInstr.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cmFunc.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cm4_simd.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (User\gd32f3x0_libopt.h)(0x68849119)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_adc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_gpio.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_rcu.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_misc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_fmc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_exti.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_syscfg.h)(0x67BD7BDB)
F (.\Drivers\GD32F3x0_StdPeriph_Driver\src\gd32f3x0_misc.c)(0x67BD7BDB)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ./Drivers/CMSIS/Include -I ./Drivers/GD32F3x0_StdPeriph_Driver/inc -I ./User -I ./Drivers/CMSIS/Source/ARM

-I./RTE/_GD32F310F8P6

-ID:/Keil5.27/Pack/GigaDevice/GD32F3x0_DFP/3.3.0/Device/Include

-ID:/Keil5.27/Core/ARM/CMSIS/Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o ./output/gd32f3x0_misc.o -MD)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_misc.h)(0x67BD7BDB)
I (Drivers\CMSIS\Include\gd32f3x0.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCLANG\include\stdint.h)(0x5C40E42A)
I (Drivers\CMSIS\Include\core_cmInstr.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cmFunc.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cm4_simd.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (User\gd32f3x0_libopt.h)(0x68849119)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_adc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_gpio.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_rcu.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_usart.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_fmc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_exti.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_syscfg.h)(0x67BD7BDB)
F (.\Drivers\GD32F3x0_StdPeriph_Driver\src\gd32f3x0_fmc.c)(0x67BD7BDB)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ./Drivers/CMSIS/Include -I ./Drivers/GD32F3x0_StdPeriph_Driver/inc -I ./User -I ./Drivers/CMSIS/Source/ARM

-I./RTE/_GD32F310F8P6

-ID:/Keil5.27/Pack/GigaDevice/GD32F3x0_DFP/3.3.0/Device/Include

-ID:/Keil5.27/Core/ARM/CMSIS/Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o ./output/gd32f3x0_fmc.o -MD)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_fmc.h)(0x67BD7BDB)
I (Drivers\CMSIS\Include\gd32f3x0.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCLANG\include\stdint.h)(0x5C40E42A)
I (Drivers\CMSIS\Include\core_cmInstr.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cmFunc.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cm4_simd.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (User\gd32f3x0_libopt.h)(0x68849119)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_adc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_gpio.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_rcu.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_usart.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_misc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_exti.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_syscfg.h)(0x67BD7BDB)
F (.\Drivers\GD32F3x0_StdPeriph_Driver\src\gd32f3x0_exti.c)(0x67BD7BDB)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ./Drivers/CMSIS/Include -I ./Drivers/GD32F3x0_StdPeriph_Driver/inc -I ./User -I ./Drivers/CMSIS/Source/ARM

-I./RTE/_GD32F310F8P6

-ID:/Keil5.27/Pack/GigaDevice/GD32F3x0_DFP/3.3.0/Device/Include

-ID:/Keil5.27/Core/ARM/CMSIS/Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o ./output/gd32f3x0_exti.o -MD)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_exti.h)(0x67BD7BDB)
I (Drivers\CMSIS\Include\gd32f3x0.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCLANG\include\stdint.h)(0x5C40E42A)
I (Drivers\CMSIS\Include\core_cmInstr.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cmFunc.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cm4_simd.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (User\gd32f3x0_libopt.h)(0x68849119)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_adc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_gpio.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_rcu.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_usart.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_misc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_fmc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_syscfg.h)(0x67BD7BDB)
F (.\Drivers\GD32F3x0_StdPeriph_Driver\src\gd32f3x0_syscfg.c)(0x67BD7BDB)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ./Drivers/CMSIS/Include -I ./Drivers/GD32F3x0_StdPeriph_Driver/inc -I ./User -I ./Drivers/CMSIS/Source/ARM

-I./RTE/_GD32F310F8P6

-ID:/Keil5.27/Pack/GigaDevice/GD32F3x0_DFP/3.3.0/Device/Include

-ID:/Keil5.27/Core/ARM/CMSIS/Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o ./output/gd32f3x0_syscfg.o -MD)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_syscfg.h)(0x67BD7BDB)
I (Drivers\CMSIS\Include\gd32f3x0.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCLANG\include\stdint.h)(0x5C40E42A)
I (Drivers\CMSIS\Include\core_cmInstr.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cmFunc.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\core_cm4_simd.h)(0x67BD7BDA)
I (Drivers\CMSIS\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (User\gd32f3x0_libopt.h)(0x68849119)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_adc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_gpio.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_rcu.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_usart.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_misc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_fmc.h)(0x67BD7BDB)
I (Drivers\GD32F3x0_StdPeriph_Driver\inc\gd32f3x0_exti.h)(0x67BD7BDB)
F (.\Drivers\CMSIS\Source\ARM\startup_gd32f3x0.s)(0x67BD7BDA)(--cpu Cortex-M4.fp -g --pd "__MICROLIB SETA 1"

-I.\RTE\_GD32F310F8P6

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

--pd "__UVISION_VERSION SETA 527"

--pd "GD32F310 SETA 1"

--list .\listings\startup_gd32f3x0.lst

--xref -o .\output\startup_gd32f3x0.o

--depend .\output\startup_gd32f3x0.d)

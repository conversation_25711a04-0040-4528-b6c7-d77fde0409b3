<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Output\QuadrantSensor.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Output\QuadrantSensor.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 6120001: Last Updated: Sat Aug  2 15:13:36 2025
<BR><P>
<H3>Maximum Stack Usage =        136 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; rs485_process_command &rArr; rs485_send_quadrant_data_response &rArr; rs485_send_data
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[16]">ADC_CMP_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[16]">ADC_CMP_IRQHandler</a><BR>
 <LI><a href="#[4]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[4]">BusFault_Handler</a><BR>
 <LI><a href="#[7]">DebugMon_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[7]">DebugMon_Handler</a><BR>
 <LI><a href="#[2]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[2]">HardFault_Handler</a><BR>
 <LI><a href="#[3]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[3]">MemManage_Handler</a><BR>
 <LI><a href="#[1]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1]">NMI_Handler</a><BR>
 <LI><a href="#[8]">PendSV_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[8]">PendSV_Handler</a><BR>
 <LI><a href="#[6]">SVC_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[6]">SVC_Handler</a><BR>
 <LI><a href="#[5]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[5]">UsageFault_Handler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[16]">ADC_CMP_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[4]">BusFault_Handler</a> from gd32f3x0_it.o(.text.BusFault_Handler) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[26]">CEC_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[13]">DMA_Channel0_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[14]">DMA_Channel1_2_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[15]">DMA_Channel3_4_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[2a]">DMA_Channel5_6_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[7]">DebugMon_Handler</a> from gd32f3x0_it.o(.text.DebugMon_Handler) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[f]">EXTI0_1_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[10]">EXTI2_3_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[11]">EXTI4_15_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[d]">FMC_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[2]">HardFault_Handler</a> from gd32f3x0_it.o(.text.HardFault_Handler) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[27]">I2C0_ER_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[20]">I2C0_EV_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[28]">I2C1_ER_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[21]">I2C1_EV_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[b]">LVD_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[3]">MemManage_Handler</a> from gd32f3x0_it.o(.text.MemManage_Handler) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[1]">NMI_Handler</a> from gd32f3x0_it.o(.text.NMI_Handler) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[8]">PendSV_Handler</a> from gd32f3x0_it.o(.text.PendSV_Handler) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[e]">RCU_CTC_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[c]">RTC_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[0]">Reset_Handler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[22]">SPI0_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[23]">SPI1_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[6]">SVC_Handler</a> from gd32f3x0_it.o(.text.SVC_Handler) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[9]">SysTick_Handler</a> from gd32f3x0_it.o(.text.SysTick_Handler) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[2d]">SystemInit</a> from system_gd32f3x0.o(.text.SystemInit) referenced from startup_gd32f3x0.o(.text)
 <LI><a href="#[17]">TIMER0_BRK_UP_TRG_COM_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[18]">TIMER0_Channel_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[1c]">TIMER13_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[1d]">TIMER14_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[1e]">TIMER15_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[1f]">TIMER16_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[19]">TIMER1_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[1a]">TIMER2_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[1b]">TIMER5_DAC_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[12]">TSI_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[24]">USART0_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[25]">USART1_IRQHandler</a> from gd32f3x0_it.o(.text.USART1_IRQHandler) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[2b]">USBFS_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[29]">USBFS_WKUP_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[5]">UsageFault_Handler</a> from gd32f3x0_it.o(.text.UsageFault_Handler) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[a]">WWDGT_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[2e]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_gd32f3x0.o(.text)
 <LI><a href="#[30]">gpio_mode_set</a> from gd32f3x0_gpio.o(.text.gpio_mode_set) referenced from rs485_comm.o(.text.rs485_init)
 <LI><a href="#[2c]">main</a> from main.o(.text.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
 <LI><a href="#[2f]">rcu_periph_clock_enable</a> from gd32f3x0_rcu.o(.text.rcu_periph_clock_enable) referenced from rs485_comm.o(.text.rs485_init)
 <LI><a href="#[31]">soft_i2c_delay</a> from soft_i2c.o(.text.soft_i2c_delay) referenced from soft_i2c.o(.text.soft_i2c_stop)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[2e]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(.text)
</UL>
<P><STRONG><a name="[7c]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[32]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[34]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[7d]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[7e]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[7f]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[80]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[81]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[0]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>ADC_CMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_CMP_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_CMP_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>CEC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>DMA_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>DMA_Channel1_2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>DMA_Channel3_4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>DMA_Channel5_6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>EXTI0_1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>EXTI2_3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>EXTI4_15_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>I2C0_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>I2C0_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>LVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>RCU_CTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>RTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>SPI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>TIMER0_BRK_UP_TRG_COM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>TIMER0_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>TIMER13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>TIMER14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>TIMER15_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>TIMER16_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>TIMER1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>TIMER2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>TIMER5_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>TSI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>USART0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>USBFS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>USBFS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>WWDGT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_data
</UL>

<P><STRONG><a name="[82]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[83]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[33]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[84]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[4]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f3x0_it.o(.text.BusFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f3x0_it.o(.text.DebugMon_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DebugMon_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DebugMon_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[2]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f3x0_it.o(.text.HardFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[3]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f3x0_it.o(.text.MemManage_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[1]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f3x0_it.o(.text.NMI_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f3x0_it.o(.text.PendSV_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[6]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f3x0_it.o(.text.SVC_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>SysTick_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f3x0_it.o(.text.SysTick_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_decrement
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>SystemInit</STRONG> (Thumb, 696 bytes, Stack size 8 bytes, system_gd32f3x0.o(.text.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SystemInit
</UL>
<BR>[Calls]<UL><LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_vector_table_set
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(.text)
</UL>
<P><STRONG><a name="[25]"></a>USART1_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f3x0_it.o(.text.USART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USART1_IRQHandler &rArr; rs485_uart_irq_handler &rArr; rs485_process_received_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_uart_irq_handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f3x0_it.o(.text.UsageFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>board_init</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, main.o(.text.board_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = board_init &rArr; cm1103_init &rArr; cm1103_i2c_scan &rArr; soft_i2c_read_reg &rArr; soft_i2c_send_byte &rArr; soft_i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm1103_init
</UL>
<BR>[Called By]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[3b]"></a>cm1103_config_channel</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, cm1103_driver.o(.text.cm1103_config_channel))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = cm1103_config_channel &rArr; cm1103_write_register &rArr; soft_i2c_write_reg &rArr; soft_i2c_send_byte &rArr; soft_i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm1103_write_register
</UL>
<BR>[Called By]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm1103_update_continuous_data
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm1103_init
</UL>

<P><STRONG><a name="[3d]"></a>cm1103_i2c_scan</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, cm1103_driver.o(.text.cm1103_i2c_scan))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = cm1103_i2c_scan &rArr; soft_i2c_read_reg &rArr; soft_i2c_send_byte &rArr; soft_i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_read_reg
</UL>
<BR>[Called By]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm1103_init
</UL>

<P><STRONG><a name="[39]"></a>cm1103_init</STRONG> (Thumb, 140 bytes, Stack size 16 bytes, cm1103_driver.o(.text.cm1103_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = cm1103_init &rArr; cm1103_i2c_scan &rArr; soft_i2c_read_reg &rArr; soft_i2c_send_byte &rArr; soft_i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_read_reg
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_init
<LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm1103_write_register
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm1103_read_register
<LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm1103_i2c_scan
<LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm1103_config_channel
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
</UL>
<BR>[Called By]<UL><LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;board_init
</UL>

<P><STRONG><a name="[41]"></a>cm1103_is_cached_data_valid</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, cm1103_driver.o(.text.cm1103_is_cached_data_valid))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = cm1103_is_cached_data_valid
</UL>
<BR>[Calls]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_get_tick
</UL>
<BR>[Called By]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm1103_read_all_channels
</UL>

<P><STRONG><a name="[43]"></a>cm1103_read_all_channels</STRONG> (Thumb, 152 bytes, Stack size 8 bytes, cm1103_driver.o(.text.cm1103_read_all_channels))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = cm1103_read_all_channels &rArr; cm1103_is_cached_data_valid
</UL>
<BR>[Calls]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm1103_is_cached_data_valid
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_get_tick
</UL>
<BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_quadrant_data_response
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[45]"></a>cm1103_read_conversion</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, cm1103_driver.o(.text.cm1103_read_conversion))
<BR><BR>[Called By]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm1103_update_continuous_data
</UL>

<P><STRONG><a name="[40]"></a>cm1103_read_register</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, cm1103_driver.o(.text.cm1103_read_register))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = cm1103_read_register &rArr; soft_i2c_read_reg &rArr; soft_i2c_send_byte &rArr; soft_i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_read_reg
</UL>
<BR>[Called By]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm1103_init
</UL>

<P><STRONG><a name="[46]"></a>cm1103_reset_cache</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, cm1103_driver.o(.text.cm1103_reset_cache))
<BR><BR>[Called By]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm1103_update_continuous_data
</UL>

<P><STRONG><a name="[44]"></a>cm1103_update_continuous_data</STRONG> (Thumb, 136 bytes, Stack size 24 bytes, cm1103_driver.o(.text.cm1103_update_continuous_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = cm1103_update_continuous_data &rArr; cm1103_config_channel &rArr; cm1103_write_register &rArr; soft_i2c_write_reg &rArr; soft_i2c_send_byte &rArr; soft_i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm1103_reset_cache
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm1103_read_conversion
<LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm1103_config_channel
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_get_tick
</UL>
<BR>[Called By]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[3c]"></a>cm1103_write_register</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, cm1103_driver.o(.text.cm1103_write_register))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = cm1103_write_register &rArr; soft_i2c_write_reg &rArr; soft_i2c_send_byte &rArr; soft_i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_write_reg
</UL>
<BR>[Called By]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm1103_config_channel
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm1103_init
</UL>

<P><STRONG><a name="[3a]"></a>delay_1ms</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, systick.o(.text.delay_1ms))
<BR><BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_data
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_process_command
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm1103_init
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;board_init
</UL>

<P><STRONG><a name="[35]"></a>delay_decrement</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, systick.o(.text.delay_decrement))
<BR><BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[51]"></a>gpio_af_set</STRONG> (Thumb, 112 bytes, Stack size 24 bytes, gd32f3x0_gpio.o(.text.gpio_af_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
</UL>

<P><STRONG><a name="[52]"></a>gpio_bit_reset</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f3x0_gpio.o(.text.gpio_bit_reset))
<BR><BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_data
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_wait_ack
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_stop
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_start
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_send_nack
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_send_byte
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_send_ack
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_read_byte
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
</UL>

<P><STRONG><a name="[63]"></a>gpio_bit_set</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f3x0_gpio.o(.text.gpio_bit_set))
<BR><BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_data
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_wait_ack
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_stop
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_start
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_send_nack
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_send_byte
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_send_ack
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_read_byte
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_init
</UL>

<P><STRONG><a name="[69]"></a>gpio_input_bit_get</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f3x0_gpio.o(.text.gpio_input_bit_get))
<BR><BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_wait_ack
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_read_byte
</UL>

<P><STRONG><a name="[30]"></a>gpio_mode_set</STRONG> (Thumb, 82 bytes, Stack size 32 bytes, gd32f3x0_gpio.o(.text.gpio_mode_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> rs485_comm.o(.text.rs485_init)
</UL>
<P><STRONG><a name="[50]"></a>gpio_output_options_set</STRONG> (Thumb, 120 bytes, Stack size 20 bytes, gd32f3x0_gpio.o(.text.gpio_output_options_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_output_options_set
</UL>
<BR>[Called By]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_init
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
</UL>

<P><STRONG><a name="[2c]"></a>main</STRONG> (Thumb, 128 bytes, Stack size 32 bytes, main.o(.text.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = main &rArr; rs485_process_command &rArr; rs485_send_quadrant_data_response &rArr; rs485_send_data
</UL>
<BR>[Calls]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_get_tick
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_config
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_system_ready
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_quadrant_data_auto
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_error_response
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_process_command
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_get_received_command
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm1103_update_continuous_data
<LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm1103_read_all_channels
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;board_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[5b]"></a>nvic_irq_enable</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, gd32f3x0_misc.o(.text.nvic_irq_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nvic_irq_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
</UL>

<P><STRONG><a name="[36]"></a>nvic_vector_table_set</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f3x0_misc.o(.text.nvic_vector_table_set))
<BR><BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[74]"></a>rcu_ahb_clock_config</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, gd32f3x0_rcu.o(.text.rcu_ahb_clock_config))
<BR><BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>

<P><STRONG><a name="[75]"></a>rcu_apb1_clock_config</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, gd32f3x0_rcu.o(.text.rcu_apb1_clock_config))
<BR><BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>

<P><STRONG><a name="[76]"></a>rcu_apb2_clock_config</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, gd32f3x0_rcu.o(.text.rcu_apb2_clock_config))
<BR><BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>

<P><STRONG><a name="[79]"></a>rcu_clock_freq_get</STRONG> (Thumb, 428 bytes, Stack size 20 bytes, gd32f3x0_rcu.o(.text.rcu_clock_freq_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
</UL>

<P><STRONG><a name="[70]"></a>rcu_osci_on</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, gd32f3x0_rcu.o(.text.rcu_osci_on))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rcu_osci_on
</UL>
<BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>

<P><STRONG><a name="[71]"></a>rcu_osci_stab_wait</STRONG> (Thumb, 256 bytes, Stack size 0 bytes, gd32f3x0_rcu.o(.text.rcu_osci_stab_wait))
<BR><BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>

<P><STRONG><a name="[2f]"></a>rcu_periph_clock_enable</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, gd32f3x0_rcu.o(.text.rcu_periph_clock_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> rs485_comm.o(.text.rs485_init)
</UL>
<P><STRONG><a name="[7b]"></a>rcu_periph_reset_disable</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, gd32f3x0_rcu.o(.text.rcu_periph_reset_disable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
</UL>

<P><STRONG><a name="[7a]"></a>rcu_periph_reset_enable</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, gd32f3x0_rcu.o(.text.rcu_periph_reset_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rcu_periph_reset_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
</UL>

<P><STRONG><a name="[73]"></a>rcu_pll_config</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, gd32f3x0_rcu.o(.text.rcu_pll_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rcu_pll_config
</UL>
<BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>

<P><STRONG><a name="[72]"></a>rcu_pll_preselection_config</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f3x0_rcu.o(.text.rcu_pll_preselection_config))
<BR><BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>

<P><STRONG><a name="[77]"></a>rcu_system_clock_source_config</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, gd32f3x0_rcu.o(.text.rcu_system_clock_source_config))
<BR><BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>

<P><STRONG><a name="[78]"></a>rcu_system_clock_source_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f3x0_rcu.o(.text.rcu_system_clock_source_get))
<BR><BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>

<P><STRONG><a name="[60]"></a>rs485_calculate_checksum</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, rs485_comm.o(.text.rs485_calculate_checksum))
<BR><BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_quadrant_data_response
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_heartbeat_response
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_process_received_byte
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_system_ready
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_quadrant_data_auto
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_error_response
</UL>

<P><STRONG><a name="[4c]"></a>rs485_get_received_command</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, rs485_comm.o(.text.rs485_get_received_command))
<BR><BR>[Called By]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[4a]"></a>rs485_init</STRONG> (Thumb, 260 bytes, Stack size 24 bytes, rs485_comm.o(.text.rs485_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = rs485_init &rArr; usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_word_length_set
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_transmit_config
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_stop_bit_set
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_receive_config
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_parity_config
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_enable
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_enable
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
</UL>
<BR>[Called By]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[4d]"></a>rs485_process_command</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, rs485_comm.o(.text.rs485_process_command))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = rs485_process_command &rArr; rs485_send_quadrant_data_response &rArr; rs485_send_data
</UL>
<BR>[Calls]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_quadrant_data_response
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_heartbeat_response
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_error_response
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
</UL>
<BR>[Called By]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[5f]"></a>rs485_process_received_byte</STRONG> (Thumb, 136 bytes, Stack size 16 bytes, rs485_comm.o(.text.rs485_process_received_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = rs485_process_received_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_calculate_checksum
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_get_tick
</UL>
<BR>[Called By]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_uart_irq_handler
</UL>

<P><STRONG><a name="[61]"></a>rs485_send_data</STRONG> (Thumb, 160 bytes, Stack size 40 bytes, rs485_comm.o(.text.rs485_send_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = rs485_send_data
</UL>
<BR>[Calls]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_transmit
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_quadrant_data_response
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_heartbeat_response
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_system_ready
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_quadrant_data_auto
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_error_response
</UL>

<P><STRONG><a name="[4f]"></a>rs485_send_error_response</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, rs485_comm.o(.text.rs485_send_error_response))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = rs485_send_error_response &rArr; rs485_send_data
</UL>
<BR>[Calls]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_data
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_calculate_checksum
</UL>
<BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_process_command
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[5d]"></a>rs485_send_heartbeat_response</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, rs485_comm.o(.text.rs485_send_heartbeat_response))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = rs485_send_heartbeat_response &rArr; rs485_send_data
</UL>
<BR>[Calls]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_data
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_calculate_checksum
</UL>
<BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_process_command
</UL>

<P><STRONG><a name="[4e]"></a>rs485_send_quadrant_data_auto</STRONG> (Thumb, 152 bytes, Stack size 48 bytes, rs485_comm.o(.text.rs485_send_quadrant_data_auto))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = rs485_send_quadrant_data_auto &rArr; rs485_send_data
</UL>
<BR>[Calls]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_data
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_calculate_checksum
</UL>
<BR>[Called By]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[5e]"></a>rs485_send_quadrant_data_response</STRONG> (Thumb, 132 bytes, Stack size 56 bytes, rs485_comm.o(.text.rs485_send_quadrant_data_response))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = rs485_send_quadrant_data_response &rArr; rs485_send_data
</UL>
<BR>[Calls]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_data
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_calculate_checksum
<LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm1103_read_all_channels
</UL>
<BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_process_command
</UL>

<P><STRONG><a name="[4b]"></a>rs485_send_system_ready</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, rs485_comm.o(.text.rs485_send_system_ready))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = rs485_send_system_ready &rArr; rs485_send_data
</UL>
<BR>[Calls]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_data
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_calculate_checksum
</UL>
<BR>[Called By]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[37]"></a>rs485_uart_irq_handler</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, rs485_comm.o(.text.rs485_uart_irq_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = rs485_uart_irq_handler &rArr; rs485_process_received_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_flag_get
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_receive
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_process_received_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[31]"></a>soft_i2c_delay</STRONG> (Thumb, 26 bytes, Stack size 4 bytes, soft_i2c.o(.text.soft_i2c_delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = soft_i2c_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_wait_ack
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_send_byte
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_read_byte
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> soft_i2c.o(.text.soft_i2c_stop)
</UL>
<P><STRONG><a name="[3f]"></a>soft_i2c_init</STRONG> (Thumb, 114 bytes, Stack size 8 bytes, soft_i2c.o(.text.soft_i2c_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = soft_i2c_init &rArr; gpio_mode_set
</UL>
<BR>[Calls]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_delay
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
</UL>
<BR>[Called By]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm1103_init
</UL>

<P><STRONG><a name="[68]"></a>soft_i2c_read_byte</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, soft_i2c.o(.text.soft_i2c_read_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = soft_i2c_read_byte &rArr; soft_i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_delay
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_bit_get
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_read_reg
</UL>

<P><STRONG><a name="[3e]"></a>soft_i2c_read_reg</STRONG> (Thumb, 98 bytes, Stack size 16 bytes, soft_i2c.o(.text.soft_i2c_read_reg))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = soft_i2c_read_reg &rArr; soft_i2c_send_byte &rArr; soft_i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_wait_ack
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_stop
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_start
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_send_nack
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_send_byte
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_send_ack
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_read_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm1103_read_register
<LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm1103_i2c_scan
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm1103_init
</UL>

<P><STRONG><a name="[6d]"></a>soft_i2c_send_ack</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, soft_i2c.o(.text.soft_i2c_send_ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = soft_i2c_send_ack
</UL>
<BR>[Calls]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_read_reg
</UL>

<P><STRONG><a name="[6b]"></a>soft_i2c_send_byte</STRONG> (Thumb, 98 bytes, Stack size 16 bytes, soft_i2c.o(.text.soft_i2c_send_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = soft_i2c_send_byte &rArr; soft_i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_delay
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_write_reg
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_read_reg
</UL>

<P><STRONG><a name="[6e]"></a>soft_i2c_send_nack</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, soft_i2c.o(.text.soft_i2c_send_nack))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = soft_i2c_send_nack
</UL>
<BR>[Calls]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_read_reg
</UL>

<P><STRONG><a name="[6a]"></a>soft_i2c_start</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, soft_i2c.o(.text.soft_i2c_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = soft_i2c_start
</UL>
<BR>[Calls]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_write_reg
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_read_reg
</UL>

<P><STRONG><a name="[6f]"></a>soft_i2c_stop</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, soft_i2c.o(.text.soft_i2c_stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = soft_i2c_stop
</UL>
<BR>[Calls]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_write_reg
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_read_reg
</UL>

<P><STRONG><a name="[6c]"></a>soft_i2c_wait_ack</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, soft_i2c.o(.text.soft_i2c_wait_ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = soft_i2c_wait_ack &rArr; soft_i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_delay
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_bit_get
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_write_reg
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_read_reg
</UL>

<P><STRONG><a name="[47]"></a>soft_i2c_write_reg</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, soft_i2c.o(.text.soft_i2c_write_reg))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = soft_i2c_write_reg &rArr; soft_i2c_send_byte &rArr; soft_i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_wait_ack
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_stop
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_start
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_send_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm1103_write_register
</UL>

<P><STRONG><a name="[48]"></a>system_clock_config</STRONG> (Thumb, 80 bytes, Stack size 8 bytes, main.o(.text.system_clock_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = system_clock_config &rArr; rcu_pll_config
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_system_clock_source_get
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_system_clock_source_config
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_pll_preselection_config
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_pll_config
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_stab_wait
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_on
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_apb2_clock_config
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_apb1_clock_config
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_ahb_clock_config
</UL>
<BR>[Called By]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[49]"></a>systick_config</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, systick.o(.text.systick_config))
<BR><BR>[Called By]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[42]"></a>systick_get_tick</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, systick.o(.text.systick_get_tick))
<BR><BR>[Called By]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_process_received_byte
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm1103_is_cached_data_valid
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm1103_update_continuous_data
<LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm1103_read_all_channels
</UL>

<P><STRONG><a name="[54]"></a>usart_baudrate_set</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, gd32f3x0_usart.o(.text.usart_baudrate_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
</UL>

<P><STRONG><a name="[67]"></a>usart_data_receive</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f3x0_usart.o(.text.usart_data_receive))
<BR><BR>[Called By]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_uart_irq_handler
</UL>

<P><STRONG><a name="[64]"></a>usart_data_transmit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f3x0_usart.o(.text.usart_data_transmit))
<BR><BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_data
</UL>

<P><STRONG><a name="[53]"></a>usart_deinit</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, gd32f3x0_usart.o(.text.usart_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usart_deinit &rArr; rcu_periph_reset_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
</UL>

<P><STRONG><a name="[5c]"></a>usart_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f3x0_usart.o(.text.usart_enable))
<BR><BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
</UL>

<P><STRONG><a name="[65]"></a>usart_flag_get</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gd32f3x0_usart.o(.text.usart_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_data
</UL>

<P><STRONG><a name="[5a]"></a>usart_interrupt_enable</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f3x0_usart.o(.text.usart_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
</UL>

<P><STRONG><a name="[66]"></a>usart_interrupt_flag_get</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, gd32f3x0_usart.o(.text.usart_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_uart_irq_handler
</UL>

<P><STRONG><a name="[57]"></a>usart_parity_config</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f3x0_usart.o(.text.usart_parity_config))
<BR><BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
</UL>

<P><STRONG><a name="[58]"></a>usart_receive_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f3x0_usart.o(.text.usart_receive_config))
<BR><BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
</UL>

<P><STRONG><a name="[56]"></a>usart_stop_bit_set</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f3x0_usart.o(.text.usart_stop_bit_set))
<BR><BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
</UL>

<P><STRONG><a name="[59]"></a>usart_transmit_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f3x0_usart.o(.text.usart_transmit_config))
<BR><BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
</UL>

<P><STRONG><a name="[55]"></a>usart_word_length_set</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f3x0_usart.o(.text.usart_word_length_set))
<BR><BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
</UL>

<P><STRONG><a name="[85]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[86]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[87]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)
<P>
<H3>
Local Symbols
</H3><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
